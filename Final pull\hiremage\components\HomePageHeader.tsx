import React, { useEffect, useState } from "react";
import HomePageNav from "./HomePageNav";
import { useSelector } from "react-redux";
import { IRootState } from "@/store";
import Link from "next/link";
import Head from "next/head";

export default function HomePageHeader({ landingData }: any) {
  const { user, isLoggedIn } = useSelector(
    (state: IRootState) => state.userSlice
  );

  return (
    <section className="animate-gradient-for-home-page-banner  curve-custom">
      <HomePageNav
        landingLogo={landingData?.landing_data?.landing_page_logo_url}
      />
      <Head>
        <link
          rel="icon"
          href={landingData?.settings?.site_fav_icon}
          type="image/x-icon"
        />
        <title>{landingData?.settings?.meta_title}</title>
        <meta
          name="description"
          content={landingData?.settings?.meta_description}
        />
      </Head>
      <header className="relative block " id="home">
        <div className="container px-5 md:px-10">
          <div className="mx-auto w-full max-w-7xl">
            <div className="py-16 md:py-24 lg:py-32">
              <div className="mx-auto mt-10 grid grid-cols-1 items-center gap-x-4 text-white lg:grid-cols-2">
                <div className="py-10 lg:py-24">
                  <div className="col-span-3">
                    <h1 className="text-2xl font-bold text-white md:text-5xl ">
                      {landingData?.landing_data?.landing_page_first_title}
                    </h1>
                  </div>
                  <div className="col-span-2">
                    <div className="mx-auto mb-5 md:mb-6 lg:mb-8">
                      <p className="text-xs md:text-base">
                        {
                          landingData?.landing_data
                            ?.landing_page_first_description
                        }
                      </p>
                      <div className='flex flex-col items-center justify-start gap-4 sm:flex-row'>
                        {/* Download for Windows Button (UPDATED STYLING FOR PILL SHAPE) */}
                        <a
                          href='https://github.com/bitidea-coder/clueiva/releases/latest/download/clueiva-setup.exe'
                          download
                          className='inline-flex items-center gap-3 rounded-full bg-black py-3 pl-7.5 pr-4 font-satoshi font-medium text-white transition-all duration-300 hover:bg-opacity-90 dark:bg-primary'
                        >
                          <span>Download for Windows</span>
                          {/* SVG Icon from sassbold-pro-main-main */}
                          <span className='inline-flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white dark:bg-white dark:text-primary'>
                            <svg
                              className='fill-current'
                              width='16'
                              height='16'
                              viewBox='0 0 24 24'
                              fill='none'
                              xmlns='http://www.w3.org/2000/svg'
                            >
                              <path
                                fillRule='evenodd'
                                clipRule='evenodd'
                                d='M12 3C12.4142 3 12.75 3.33579 12.75 3.75V14.4697L16.4697 10.75C16.7626 10.4571 17.2374 10.4571 17.5303 10.75C17.8232 11.0429 17.8232 11.5178 17.5303 11.8107L12.5303 16.8107C12.2374 17.1036 11.7626 17.1036 11.4697 16.8107L6.46967 11.8107C6.17678 11.5178 6.17678 11.0429 6.46967 10.75C6.76256 10.4571 7.23744 10.4571 7.53033 10.75L11.25 14.4697V3.75C11.25 3.33579 11.5858 3 12 3ZM3.75 15C4.16421 15 4.5 15.3358 4.5 15.75V18.75C4.5 19.1642 4.83579 19.5 5.25 19.5H18.75C19.1642 19.5 19.5 19.1642 19.5 18.75V15.75C19.5 15.3358 19.8358 15 20.25 15C20.6642 15 21 15.3358 21 15.75V18.75C21 20.0711 19.9211 21 18.75 21H5.25C4.07893 21 3 20.0711 3 18.75V15.75C3 15.3358 3.33579 15 3.75 15Z'
                              />
                            </svg>
                          </span>
                        </a>

                        {/* Download for Mac Button (UPDATED STYLING FOR PILL SHAPE & "Coming Soon") */}
                        <a
                          href='#' // Changed href to '#' as it's coming soon
                          // Same styling, plus ml-4, opacity and cursor-not-allowed
                          className='inline-flex items-center gap-3 rounded-full bg-black py-3 pl-7.5 pr-4 font-satoshi font-medium text-white transition-all duration-300 hover:bg-opacity-90 dark:bg-primary ml-4 opacity-70 cursor-not-allowed'
                        >
                          <span>Download for Mac</span>
                          {/* SVG Icon from sassbold-pro-main-main */}
                          <span className='inline-flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white dark:bg-white dark:text-primary'>
                            <svg
                              className='fill-current'
                              width='16'
                              height='16'
                              viewBox='0 0 24 24'
                              fill='none'
                              xmlns='http://www.w3.org/2000/svg'
                            >
                              <path
                                fillRule='evenodd'
                                clipRule='evenodd'
                                d='M12 3C12.4142 3 12.75 3.33579 12.75 3.75V14.4697L16.4697 10.75C16.7626 10.4571 17.2374 10.4571 17.5303 10.75C17.8232 11.0429 17.8232 11.5178 17.5303 11.8107L12.5303 16.8107C12.2374 17.1036 11.7626 17.1036 11.4697 16.8107L6.46967 11.8107C6.17678 11.5178 6.17678 11.0429 6.46967 10.75C6.76256 10.4571 7.23744 10.4571 7.53033 10.75L11.25 14.4697V3.75C11.25 3.33579 11.5858 3 12 3ZM3.75 15C4.16421 15 4.5 15.3358 4.5 15.75V18.75C4.5 19.1642 4.83579 19.5 5.25 19.5H18.75C19.1642 19.5 19.5 19.1642 19.5 18.75V15.75C19.5 15.3358 19.8358 15 20.25 15C20.6642 15 21 15.3358 21 15.75V18.75C21 20.0711 19.9211 21 18.75 21H5.25C4.07893 21 3 20.0711 3 18.75V15.75C3 15.3358 3.33579 15 3.75 15Z'
                              />
                            </svg>
                          </span>
                          <span className="ml-2 text-sm text-white-400">Coming Soon</span> {/* "Coming Soon" text */}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="floating-image mt-4 h-full rounded-[1.25rem] p-[.4375rem] lg:mt-0">
                  <img
                    className="animated-image h-full overflow-hidden rounded-[1.25rem]"
                    src={
                      landingData?.landing_data?.landing_page_first_img_url
                        ? landingData?.landing_data?.landing_page_first_img_url
                        : "/assets/images/banner_img.webp"
                    }
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</section>
);
}
