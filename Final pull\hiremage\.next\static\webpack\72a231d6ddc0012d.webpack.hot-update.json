{"c": ["webpack"], "r": ["pages/login", "pages/dashboard"], "m": ["./components/Common/GithubLogin.comp.tsx", "./components/Layouts/BlankLayout.tsx", "./node_modules/@react-oauth/google/dist/index.esm.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CHiremage%5CFinal%20pull%5Chiremage%5Cpages%5Clogin.tsx&page=%2Flogin!", "./pages/login.tsx", "./components/Cards/ProgressCardWithLimit.comp.tsx", "./components/Cards/infoCard.comp.tsx", "./components/Common/NoItemFound.comp.tsx", "./hooks/user.hook.ts", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CHiremage%5CFinal%20pull%5Chiremage%5Cpages%5Cdashboard.tsx&page=%2Fdashboard!", "./pages/dashboard.tsx"]}